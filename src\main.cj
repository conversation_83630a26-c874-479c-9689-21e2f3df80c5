package ciyuzhicheng

import std.console.*
import std.convert.*

main() {
    println("====================================")
    println("        英语单词拼写挑战            ")
    println("====================================")
    print("请输入你的名字：")
    let name = Console.stdIn.readln().getOrThrow()
    println("你好，" + name + "！欢迎参加英语单词拼写挑战游戏！")

    // 游戏变量初始化
    var playerScore = 0
    var totalQuestions = 0
    var round = 1
    var history = Array<String>(20) { "" }  // 存储历史记录
    var historyCount = 0
    var playAgain = true

    println("游戏规则：")
    println("1. 系统会显示单词的中文意思，你需要正确拼写出英文单词")
    println("2. 每答对一题得10分")
    println("3. 输入答案或输入'q'退出游戏")
    println("4. 达到50分即可挑战成功！")

    while (playAgain) {
        println("\n====================================")
        println("           第 " + round + " 轮挑战           ")
        println("====================================")
        playerScore = 0
        totalQuestions = 0
        round = 1  // 这里应该是从1开始，但循环内会递增，可能需要调整
        historyCount = 0

        // 使用一个固定循环次数来替代之前的while(true)，避免复杂的控制流
        for (i in 1..10) {  // 最多10题
            println("\n第 " + i + " 题：")
            println("当前得分：" + playerScore + " 分")

            // 预设单词库
            val wordList = arrayOf(
                Pair("苹果", "apple"),
                Pair("香蕉", "banana"),
                Pair("猫", "cat"),
                Pair("狗", "dog"),
                Pair("大象", "elephant"),
                Pair("鱼", "fish"),
                Pair("长颈鹿", "giraffe"),
                Pair("房子", "house"),
                Pair("冰淇淋", "ice cream"),
                Pair("果酱", "jam")
            )

            // 简单选择单词 - 不使用复杂的随机算法以避免类型问题
            val wordIndex = i % wordList.size  // 简单轮换选择单词
            val wordPair = wordList[wordIndex]

            // 显示中文提示
            println("请拼写单词：【" + wordPair.first + "】")

            // 获取用户输入
            print("你的答案：")
            val input = Console.stdIn.readln().getOrThrow()

            // 检查是否退出
            if (input == "q") {
                println("你选择退出挑战。")
                break
            }

            totalQuestions++
            val playerAnswer = input.trim().toLowerCase()
            val correctAnswer = wordPair.second.toLowerCase()

            // 判断答案是否正确
            var result = ""
            if (playerAnswer == correctAnswer) {
                result = "回答正确！+10分"
                playerScore += 10
            } else {
                result = "回答错误！正确答案是：" + wordPair.second
            }

            println("结果：" + result)
            println("当前得分：" + playerScore + " 分")

            // 记录历史
            val record = "第" + i + "题：【" + wordPair.first + "】" +
                    " 你的答案：" + playerAnswer +
                    " 正确答案：" + wordPair.second +
                    " " + result
            history[historyCount] = record
            historyCount++

            // 每3题显示一次历史记录
            if (i % 3 == 0 && historyCount > 0) {
                println("\n==== 答题历史 ====")
                for (j in 0 until historyCount) {
                    println(history[j])
                }
            }

            // 检查是否达到目标分数
            if (playerScore >= 50) {
                println("\n恭喜！你已达到50分，挑战成功！")
                break
            }
            
            round++  // 增加回合数
        }

        // 游戏结束统计
        println("\n====================================")
        println("           挑战结束               ")
        println("====================================")
        println("最终得分：" + playerScore + " 分")
        println("总题数：" + totalQuestions + " 题")

        // 计算正确率
        var accuracy = 0
        if (totalQuestions > 0) {
            accuracy = (playerScore / 10 * 100) / totalQuestions
        }
        println("正确率：" + accuracy + "%")

        // 根据得分给出评价
        if (playerScore >= 50) {
            println("太棒了！你成功完成了挑战！")
        } else if (totalQuestions > 0) {
            println("继续努力，下次一定能成功！")
        } else {
            println("你选择了退出挑战。")
        }

        // 显示完整历史记录
        println("\n==== 你的答题历史 ====")
        for (i in 0 until historyCount) {
            println(history[i])
        }

        // 询问是否再玩一次
        print("\n是否开始新的挑战？(y/n)：")
        val again = Console.stdIn.readln().getOrThrow()
        if (again != "y" && again != "Y") {
            playAgain = false
        }
    }

    println("\n====================================")
    println("       感谢参与单词拼写挑战       ")
    println("       欢迎下次再来！             ")
    println("====================================")
}